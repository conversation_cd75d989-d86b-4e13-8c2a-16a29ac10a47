# 串口数据Flash存储功能实现文档

## 1. 功能概述

本功能实现了将串口2接收到的"X:123,Y:23"格式数据解析并存储到SPI Flash的完整方案。

## 2. 核心数据结构

### 2.1 坐标数据结构
```c
typedef struct {
    uint32_t timestamp;    // 系统时间戳(HAL_GetTick())
    int16_t x_value;       // X坐标值 (-32768 ~ 32767)
    int16_t y_value;       // Y坐标值 (-32768 ~ 32767)
    uint16_t checksum;     // 数据校验和
    uint16_t reserved;     // 保留字段，16字节对齐
} coordinate_data_t;       // 总大小: 16字节
```

### 2.2 存储管理结构
```c
typedef struct {
    uint32_t base_address;     // 存储起始地址 0x001000
    uint32_t current_address;  // 当前写入地址
    uint16_t record_count;     // 已存储记录数
    uint16_t max_records;      // 最大记录数 1000
    uint8_t sector_full;       // 扇区满标志
} flash_storage_manager_t;
```

## 3. 核心函数实现

### 3.1 数据解析函数
```c
int parse_coordinate_data(const char* input, coordinate_data_t* output)
```
- **功能**: 解析"X:123,Y:456"格式的字符串
- **参数**: input - 输入字符串, output - 输出数据结构
- **返回**: 0成功, -1失败
- **实现**: 使用strstr查找"X:"和"Y:"标识符，atoi转换数值

### 3.2 校验和计算
```c
uint16_t calculate_checksum(const coordinate_data_t* data)
```
- **功能**: 计算数据结构的校验和
- **算法**: 将timestamp的高低16位、x_value、y_value相加

### 3.3 Flash存储函数
```c
int store_coordinate_data(const coordinate_data_t* data)
```
- **功能**: 将坐标数据存储到Flash
- **地址管理**: 自动管理存储地址，支持循环覆盖
- **扇区管理**: 自动擦除新扇区

### 3.4 数据读取函数
```c
int read_coordinate_data(uint16_t index, coordinate_data_t* data)
```
- **功能**: 从Flash读取指定索引的数据
- **验证**: 自动验证数据校验和

## 4. 存储地址规划

- **起始地址**: 0x001000 (避开test_spi_flash使用的0x000000)
- **记录大小**: 16字节/条
- **最大记录数**: 1000条
- **总占用空间**: 16KB (0x001000 - 0x004FFF)
- **扇区分配**: 4个4KB扇区

## 5. 集成方式

### 5.1 初始化
在main.c中调用`flash_storage_init()`初始化存储管理器。

### 5.2 数据处理流程
```
串口接收 -> 环形缓冲区 -> usart_proc() -> uart_data_storage_proc()
    -> parse_coordinate_data() -> store_coordinate_data() -> Flash存储
```

### 5.3 调用时机
在`usart_proc()`函数中，处理完串口2数据后调用`uart_data_storage_proc()`。

## 6. 测试功能

### 6.1 测试函数
- `test_coordinate_storage()`: 完整功能测试
- `test_read_stored_data()`: 读取验证测试

### 6.2 测试数据
```c
"X:100,Y:200"
"X:-50,Y:75" 
"X:999,Y:-123"
```

## 7. 错误处理

### 7.1 解析错误
- 格式不匹配: 返回-1，记录错误日志
- 数值超范围: 自动截断到int16_t范围

### 7.2 存储错误
- Flash写入失败: 记录错误日志
- 扇区擦除失败: 记录错误日志

## 8. 性能特性

- **写入频率**: 根据串口接收频率
- **存储延迟**: 约1-2ms (包括Flash写入时间)
- **地址管理**: O(1)时间复杂度
- **数据验证**: 校验和验证，确保数据完整性

## 9. 使用示例

### 9.1 发送测试数据
通过串口2发送: `X:123,Y:456`

### 9.2 预期输出
```
Ringbuffer2:X:123,Y:456
Parsed: X=123, Y=456, TS=12345, CS=0x1234
Stored data at 0x1000: X=123, Y=456, TS=12345
Data stored successfully: X=123, Y=456
```

## 10. 注意事项

1. **地址冲突**: 确保存储地址不与test_spi_flash冲突
2. **Flash寿命**: 控制写入频率，避免过度磨损
3. **数据完整性**: 使用校验和确保数据正确性
4. **扇区管理**: 自动擦除机制确保存储空间循环使用

## 11. 扩展功能

- 支持更多数据格式解析
- 实现数据压缩存储
- 添加数据导出功能
- 实现Flash磨损均衡
