#include "usart_app.h"
#include "gd25qxx.h"
#include <stdlib.h>
#include <string.h>

// Flash存储配置
#define STORAGE_BASE_ADDR    0x001000  // 存储起始地址
#define RECORD_SIZE          16        // 每条记录大小
#define MAX_RECORDS          1000      // 最大记录数
#define STORAGE_SECTOR_SIZE  4096      // 扇区大小

// 全局存储管理器
static flash_storage_manager_t storage_manager = {0};

/*usart1 需要数组*/
uint32_t uart1_rx_tick;
uint16_t uart1_rx_index;
uint8_t uart1_rx_buffer[128];
/*dma usart1 需要数组*/
uint8_t uart1_flag;
uint8_t uart1_dma_buffer[128];
uint8_t uart1_rx_dma_buffer[128];
/*ringbuffer 串口1*/
uint8_t ringbuffer_poll_uart1[64];
struct rt_ringbuffer ringbuffer_uart1;

/*usart2 需要数组*/
uint32_t uart_rx_tick;
uint16_t uart_rx_index;
uint8_t uart_rx_buffer[128];
/*dma usart2 需要数组*/
uint8_t uart_flag;
uint8_t uart_dma_buffer[128];
uint8_t uart_rx_dma_buffer[128];
/*ringbuffer 串口2*/
uint8_t ringbuffer_poll_uart2[64];
struct rt_ringbuffer ringbuffer_uart2;

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512]; // 临时存储格式化后的字符串
	va_list arg;      // 处理可变参数
	int len;          // 最终字符串长度

	va_start(arg, format);
	// 安全地格式化字符串到 buffer
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// 通过 HAL 库发送 buffer 中的内容
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if (huart->Instance == USART2)
	{
		uart_rx_tick = uwTick;
		uart_rx_index++;
		HAL_UART_Receive_IT(&huart2, &uart_rx_buffer[uart_rx_index], 1);
	}

}
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 1. 确认是目标串口 (USART2)
    if (huart->Instance == USART2)
    {
        // 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
        //    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
        HAL_UART_DMAStop(huart);

        // 3. 将 DMA 缓冲区中有效的数据 (Size 个字节) 复制到待处理缓冲区
        rt_ringbuffer_put(&ringbuffer_uart2, uart_rx_dma_buffer, Size);
        // 注意：这里使用了 Size，只复制实际接收到的数据
        
        // 4. 举起"到货通知旗"，告诉主循环有数据待处理

        // 5. 清空 DMA 接收缓冲区，为下次接收做准备
        //    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        // 6. **关键：重新启动下一次 DMA 空闲接收**
        //    必须再次调用，否则只会接收这一次
        HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
        
        // 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
         __HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT);
    }
		else if (huart->Instance == USART1)
		{
			// 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
        //    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
        HAL_UART_DMAStop(huart);

        // 3. 将 DMA 缓冲区中有效的数据 (Size 个字节) 复制到待处理缓冲区
        rt_ringbuffer_put(&ringbuffer_uart1, uart1_rx_dma_buffer, Size);
        // 注意：这里使用了 Size，只复制实际接收到的数据
        
        // 4. 举起"到货通知旗"，告诉主循环有数据待处理

        // 5. 清空 DMA 接收缓冲区，为下次接收做准备
        //    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
        memset(uart1_rx_dma_buffer, 0, sizeof(uart1_rx_dma_buffer));

        // 6. **关键：重新启动下一次 DMA 空闲接收**
        //    必须再次调用，否则只会接收这一次
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart1_rx_dma_buffer, sizeof(uart1_rx_dma_buffer));
        
        // 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
         __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
		}
}

void usart_proc(void)
{
	 uint16_t uart_data_len = rt_ringbuffer_data_len(&ringbuffer_uart2);
	 uint16_t uart1_data_len = rt_ringbuffer_data_len(&ringbuffer_uart1);
  if(uart_data_len > 0)
  {
    rt_ringbuffer_get(&ringbuffer_uart2, ringbuffer_poll_uart2, uart_data_len);
    ringbuffer_poll_uart2[uart_data_len] = '\0';
    /* 数据解析 */
    my_printf(&huart2, "Ringbuffer2:%s\r\n", ringbuffer_poll_uart2);

    // 新增：处理串口数据存储
    uart_data_storage_proc();

    memset(ringbuffer_poll_uart2, 0, uart_data_len);
  }
	  if(uart1_data_len > 0)
  {
    rt_ringbuffer_get(&ringbuffer_uart1, ringbuffer_poll_uart1, uart1_data_len);
    ringbuffer_poll_uart1[uart1_data_len] = '\0';
    /* 数据解析 */
    my_printf(&huart1, "Ringbuffer 1:%s\r\n", ringbuffer_poll_uart1);

    memset(ringbuffer_poll_uart1, 0, uart_data_len);
  }

}

// 计算数据校验和
uint16_t calculate_checksum(const coordinate_data_t* data)
{
    uint16_t checksum = 0;
    checksum += (uint16_t)(data->timestamp & 0xFFFF);
    checksum += (uint16_t)((data->timestamp >> 16) & 0xFFFF);
    checksum += (uint16_t)data->x_value;
    checksum += (uint16_t)data->y_value;
    return checksum;
}

// 解析坐标数据 "X:123,Y:456"
int parse_coordinate_data(const char* input, coordinate_data_t* output)
{
    if (!input || !output) return -1;

    char* x_pos = strstr(input, "X:");
    char* y_pos = strstr(input, "Y:");

    if (!x_pos || !y_pos) return -1;

    // 解析X值
    int x_val = atoi(x_pos + 2);
    // 解析Y值
    int y_val = atoi(y_pos + 2);

    // 填充数据结构
    output->timestamp = HAL_GetTick();
    output->x_value = (int16_t)x_val;
    output->y_value = (int16_t)y_val;
    output->reserved = 0;
    output->checksum = calculate_checksum(output);

    return 0;
}

// 验证坐标数据
int validate_coordinate_data(const coordinate_data_t* data)
{
    if (!data) return -1;

    uint16_t calc_checksum = calculate_checksum(data);
    if (calc_checksum != data->checksum) return -1;

    return 0;
}

// 初始化Flash存储管理器
void flash_storage_init(void)
{
    storage_manager.base_address = STORAGE_BASE_ADDR;
    storage_manager.current_address = STORAGE_BASE_ADDR;
    storage_manager.record_count = 0;
    storage_manager.max_records = MAX_RECORDS;
    storage_manager.sector_full = 0;

    my_printf(&huart1, "Flash Storage Initialized at 0x%lX\r\n", STORAGE_BASE_ADDR);
}

// 存储坐标数据到Flash
int store_coordinate_data(const coordinate_data_t* data)
{
    if (!data) return -1;

    // 检查是否需要擦除新扇区
    uint32_t sector_start = (storage_manager.current_address / STORAGE_SECTOR_SIZE) * STORAGE_SECTOR_SIZE;
    uint32_t next_sector = sector_start + STORAGE_SECTOR_SIZE;

    if (storage_manager.current_address + RECORD_SIZE > next_sector) {
        // 需要擦除下一个扇区
        my_printf(&huart1, "Erasing sector at 0x%lX\r\n", next_sector);
        spi_flash_sector_erase(next_sector);
        storage_manager.current_address = next_sector;
    }

    // 写入数据到Flash
    spi_flash_buffer_write((uint8_t*)data, storage_manager.current_address, RECORD_SIZE);

    my_printf(&huart1, "Stored data at 0x%lX: X=%d, Y=%d, TS=%lu\r\n",
              storage_manager.current_address, data->x_value, data->y_value, data->timestamp);

    // 更新地址和计数
    storage_manager.current_address += RECORD_SIZE;
    storage_manager.record_count++;

    // 检查是否达到最大记录数，循环覆盖
    if (storage_manager.record_count >= storage_manager.max_records) {
        storage_manager.current_address = storage_manager.base_address;
        storage_manager.record_count = 0;
        my_printf(&huart1, "Storage full, wrapping to beginning\r\n");
    }

    return 0;
}

// 从Flash读取坐标数据
int read_coordinate_data(uint16_t index, coordinate_data_t* data)
{
    if (!data || index >= storage_manager.max_records) return -1;

    uint32_t read_addr = storage_manager.base_address + (index * RECORD_SIZE);
    spi_flash_buffer_read((uint8_t*)data, read_addr, RECORD_SIZE);

    // 验证数据
    if (validate_coordinate_data(data) != 0) {
        my_printf(&huart1, "Data validation failed at index %d\r\n", index);
        return -1;
    }

    return 0;
}

// 串口数据存储处理主函数
void uart_data_storage_proc(void)
{
    // 检查串口2缓冲区中是否有数据
    if (strlen((char*)ringbuffer_poll_uart2) > 0) {
        coordinate_data_t coord_data;

        // 解析数据
        if (parse_coordinate_data((char*)ringbuffer_poll_uart2, &coord_data) == 0) {
            // 存储到Flash
            if (store_coordinate_data(&coord_data) == 0) {
                my_printf(&huart1, "Data stored successfully: X=%d, Y=%d\r\n",
                          coord_data.x_value, coord_data.y_value);
            } else {
                my_printf(&huart1, "Failed to store data\r\n");
            }
        } else {
            my_printf(&huart1, "Failed to parse data: %s\r\n", ringbuffer_poll_uart2);
        }
    }
}

// 测试函数：读取并显示存储的数据
void test_read_stored_data(void)
{
    coordinate_data_t test_data;
    my_printf(&huart1, "Reading stored data...\r\n");

    // 读取最近存储的几条数据
    for (int i = 0; i < 5 && i < storage_manager.record_count; i++) {
        if (read_coordinate_data(i, &test_data) == 0) {
            my_printf(&huart1, "Record %d: X=%d, Y=%d, TS=%lu\r\n",
                      i, test_data.x_value, test_data.y_value, test_data.timestamp);
        }
    }
}

// 测试坐标存储功能
void test_coordinate_storage(void)
{
    my_printf(&huart1, "\r\n=== Testing Coordinate Storage ===\r\n");

    // 测试数据解析和存储
    const char* test_strings[] = {
        "X:100,Y:200",
        "X:-50,Y:75",
        "X:999,Y:-123"
    };

    for (int i = 0; i < 3; i++) {
        coordinate_data_t test_data;
        my_printf(&huart1, "Testing: %s\r\n", test_strings[i]);

        if (parse_coordinate_data(test_strings[i], &test_data) == 0) {
            my_printf(&huart1, "Parsed: X=%d, Y=%d, TS=%lu, CS=0x%04X\r\n",
                      test_data.x_value, test_data.y_value,
                      test_data.timestamp, test_data.checksum);

            if (store_coordinate_data(&test_data) == 0) {
                my_printf(&huart1, "Storage successful\r\n");
            } else {
                my_printf(&huart1, "Storage failed\r\n");
            }
        } else {
            my_printf(&huart1, "Parse failed\r\n");
        }
        HAL_Delay(100);  // 确保时间戳不同
    }

    // 读取并验证存储的数据
    my_printf(&huart1, "\r\n=== Reading Stored Data ===\r\n");
    test_read_stored_data();

    my_printf(&huart1, "=== Test Complete ===\r\n\r\n");
}


