#ifndef USART_APP_H_
#define USART_APP_H_

#include "bsp_system.h"

// 坐标数据结构定义
typedef struct {
    uint32_t timestamp;    // 系统时间戳
    int16_t x_value;       // X坐标值
    int16_t y_value;       // Y坐标值
    uint16_t checksum;     // 数据校验和
    uint16_t reserved;     // 保留字段，16字节对齐
} coordinate_data_t;

// Flash存储管理结构
typedef struct {
    uint32_t base_address;     // 存储起始地址
    uint32_t current_address;  // 当前写入地址
    uint16_t record_count;     // 已存储记录数
    uint16_t max_records;      // 最大记录数
    uint8_t sector_full;       // 扇区满标志
} flash_storage_manager_t;

// 函数声明
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
void usart_proc(void);
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);

// 新增数据存储相关函数
int parse_coordinate_data(const char* input, coordinate_data_t* output);
int validate_coordinate_data(const coordinate_data_t* data);
uint16_t calculate_checksum(const coordinate_data_t* data);
void flash_storage_init(void);
int store_coordinate_data(const coordinate_data_t* data);
int read_coordinate_data(uint16_t index, coordinate_data_t* data);
void uart_data_storage_proc(void);
void test_read_stored_data(void);
void test_coordinate_storage(void);

#endif



