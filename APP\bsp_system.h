#ifndef _BSP_SYSTEM_H_
#define _BSP_SYSTEM_H_


#include "main.h"
#include "string.h"
#include "stdarg.h"
#include "stdio.h"

#include "scheduler.h"
#include "usart_app.h"
#include "usart.h"
#include "usart_app.h"
#include "ringbuffer.h"
#include "gd25qxx.h"
extern uint8_t uart_flag;
extern uint8_t uart_rx_dma_buffer[128];
extern uint32_t uart_rx_tick;
extern uint16_t uart_rx_index;
extern uint8_t uart_rx_buffer[128];
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart2_rx;
extern uint8_t ringbuffer_poll_uart2[64];
extern struct rt_ringbuffer ringbuffer_uart2;

extern uint32_t uart1_rx_tick;
extern uint16_t uart1_rx_index;
extern uint8_t uart1_rx_buffer[128];
extern uint8_t uart1_flag;
extern uint8_t uart1_dma_buffer[128];
extern uint8_t uart1_rx_dma_buffer[128];
extern uint8_t ringbuffer_poll_uart1[64];
extern struct rt_ringbuffer ringbuffer_uart1;

#endif






