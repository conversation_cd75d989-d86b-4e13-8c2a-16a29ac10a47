# 串口数据Flash存储系统架构设计

## 1. 架构概述

### 1.1 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   串口2 DMA     │───▶│   数据解析层    │───▶│   Flash存储层   │
│   接收系统      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   环形缓冲区    │    │   数据验证      │    │   地址管理      │
│   管理          │    │   错误处理      │    │   校验机制      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 核心组件
1. **数据接收层**: 基于现有DMA+环形缓冲区
2. **数据解析层**: 解析"X:123,Y:23"格式数据
3. **存储管理层**: Flash地址管理和数据存储
4. **验证层**: 数据校验和读取验证

## 2. 数据结构设计

### 2.1 坐标数据结构
```c
typedef struct {
    uint32_t timestamp;    // 系统时间戳(HAL_GetTick())
    int16_t x_value;       // X坐标值 (-32768 ~ 32767)
    int16_t y_value;       // Y坐标值 (-32768 ~ 32767)
    uint16_t checksum;     // 数据校验和
    uint16_t reserved;     // 保留字段，对齐到16字节
} coordinate_data_t;       // 总大小: 16字节
```

### 2.2 存储管理结构
```c
typedef struct {
    uint32_t base_address;     // 存储起始地址 0x001000
    uint32_t current_address;  // 当前写入地址
    uint16_t record_count;     // 已存储记录数
    uint16_t max_records;      // 最大记录数 1000
    uint8_t sector_full;       // 扇区满标志
} flash_storage_manager_t;
```

## 3. 接口设计

### 3.1 数据解析接口
```c
// 解析串口数据
int parse_coordinate_data(const char* input, coordinate_data_t* output);

// 数据验证
int validate_coordinate_data(const coordinate_data_t* data);

// 计算校验和
uint16_t calculate_checksum(const coordinate_data_t* data);
```

### 3.2 Flash存储接口
```c
// 初始化存储管理器
void flash_storage_init(void);

// 存储坐标数据
int store_coordinate_data(const coordinate_data_t* data);

// 读取指定索引的数据
int read_coordinate_data(uint16_t index, coordinate_data_t* data);

// 读取最新N条数据
int read_latest_data(coordinate_data_t* buffer, uint16_t count);

// 获取存储状态
void get_storage_status(uint16_t* count, uint16_t* capacity);
```

### 3.3 主处理接口
```c
// 处理串口接收到的数据
void process_uart_data(void);

// 串口数据存储主函数(集成到usart_proc)
void uart_data_storage_proc(void);
```

## 4. 存储地址规划

### 4.1 Flash地址分配
- **起始地址**: 0x001000 (4KB偏移，避开test_spi_flash使用的0x000000)
- **记录大小**: 16字节/条
- **最大记录数**: 1000条
- **总占用空间**: 16KB
- **结束地址**: 0x004FFF

### 4.2 地址管理策略
- 顺序写入，循环覆盖
- 每个扇区4KB，可存储256条记录
- 需要4个扇区(0x001000, 0x002000, 0x003000, 0x004000)

## 5. 错误处理策略

### 5.1 数据解析错误
- 格式不匹配: 丢弃数据，记录错误日志
- 数值超范围: 截断到有效范围
- 校验失败: 重新计算校验和

### 5.2 Flash操作错误
- 写入失败: 重试3次，失败后记录错误
- 读取失败: 返回错误码，不影响后续操作
- 扇区满: 擦除最旧扇区，继续写入

## 6. 性能考虑

### 6.1 写入性能
- 批量写入: 累积多条数据后一次性写入
- 异步写入: 不阻塞串口接收
- 写入频率控制: 最大1次/秒

### 6.2 读取性能
- 索引缓存: 缓存最新数据位置
- 批量读取: 支持一次读取多条数据

## 7. 集成方案

### 7.1 与现有系统集成
- 在usart_proc函数中添加数据存储处理
- 保持现有DMA接收机制不变
- 复用现有SPI Flash驱动函数

### 7.2 调用流程
```
usart_proc() 
    ├── 现有数据处理逻辑
    └── uart_data_storage_proc()
            ├── parse_coordinate_data()
            ├── validate_coordinate_data()
            └── store_coordinate_data()
```

## 8. 测试策略

### 8.1 单元测试
- 数据解析函数测试
- 校验和计算测试
- 地址管理测试

### 8.2 集成测试
- Flash读写功能测试
- 错误处理测试
- 性能压力测试

## 9. 技术风险评估

### 9.1 高风险项
- Flash写入失败导致数据丢失
- 地址管理错误导致数据覆盖

### 9.2 缓解措施
- 实现写入重试机制
- 添加地址边界检查
- 实现数据校验机制
