--cpu=Cortex-M4.fp.sp
"yao\startup_stm32f407xx.o"
"yao\main.o"
"yao\gpio.o"
"yao\dma.o"
"yao\spi.o"
"yao\usart.o"
"yao\stm32f4xx_it.o"
"yao\stm32f4xx_hal_msp.o"
"yao\scheduler.o"
"yao\usart_app.o"
"yao\gd25qxx.o"
"yao\stm32f4xx_hal_spi.o"
"yao\stm32f4xx_hal_rcc.o"
"yao\stm32f4xx_hal_rcc_ex.o"
"yao\stm32f4xx_hal_flash.o"
"yao\stm32f4xx_hal_flash_ex.o"
"yao\stm32f4xx_hal_flash_ramfunc.o"
"yao\stm32f4xx_hal_gpio.o"
"yao\stm32f4xx_hal_dma_ex.o"
"yao\stm32f4xx_hal_dma.o"
"yao\stm32f4xx_hal_pwr.o"
"yao\stm32f4xx_hal_pwr_ex.o"
"yao\stm32f4xx_hal_cortex.o"
"yao\stm32f4xx_hal.o"
"yao\stm32f4xx_hal_exti.o"
"yao\stm32f4xx_hal_tim.o"
"yao\stm32f4xx_hal_tim_ex.o"
"yao\stm32f4xx_hal_uart.o"
"yao\system_stm32f4xx.o"
"yao\ringbuffer.o"
--library_type=microlib --strict --scatter "yao\yao.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "yao.map" -o yao\yao.axf